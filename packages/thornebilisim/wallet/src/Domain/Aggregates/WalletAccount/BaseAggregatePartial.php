<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount;

/**
 * Base Aggregate Partial.
 *
 * Provides shared functionality for all aggregate partials including
 * state access, event recording, and validation patterns.
 */
abstract class BaseAggregatePartial
{
    protected WalletAccountAggregate $aggregate;
    
    public function __construct(WalletAccountAggregate $aggregate)
    {
        $this->aggregate = $aggregate;
    }
    
    /**
     * Get aggregate state property.
     */
    protected function getState(string $property): mixed
    {
        return $this->aggregate->getState($property);
    }
    
    /**
     * Record event through the main aggregate.
     */
    protected function recordThat(object $event): void
    {
        $this->aggregate->recordThat($event);
    }
    
    /**
     * Get the aggregate UUID.
     */
    protected function uuid(): string
    {
        return $this->aggregate->uuid();
    }
    
    /**
     * Abstract method for domain-specific validation.
     */
    abstract protected function validateDomain(): void;
}
