<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Carbon\Carbon;
use Thorne\Wallet\Domain\Enums\WalletAccount\RestrictionReason;
use Thorne\Wallet\Domain\Enums\WalletAccount\RestrictionType;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Wallet Restriction Applied Event.
 *
 * Fired when a restriction is applied to a wallet account.
 */
class WalletRestrictionApplied extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $restrictionUuid,
        public readonly RestrictionType $restrictionType,
        public readonly RestrictionReason $restrictionReason,
        public readonly ?Carbon $expiresAt,
        public readonly ?string $description,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'WalletRestrictionApplied';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
