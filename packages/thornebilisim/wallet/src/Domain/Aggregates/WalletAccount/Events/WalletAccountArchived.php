<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Carbon\Carbon;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * WalletAccount Archived Event.
 *
 * Fired when a wallet account is archived for historical purposes.
 */
class WalletAccountArchived extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly Carbon $archivedAt,
        public readonly ?string $reason,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'WalletAccountArchived';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
