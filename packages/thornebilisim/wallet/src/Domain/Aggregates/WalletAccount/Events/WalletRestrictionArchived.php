<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Carbon\Carbon;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Wallet Restriction Archived Event.
 *
 * Fired when a restriction is archived for historical purposes.
 */
class WalletRestrictionArchived extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $restrictionUuid,
        public readonly Carbon $archivedAt,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'WalletRestrictionArchived';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
