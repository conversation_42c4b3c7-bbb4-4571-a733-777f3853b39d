<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * WalletAccount Created Event.
 *
 * Fired when a new wallet account is created for a customer.
 */
class WalletAccountCreated extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $customerId,
        public readonly string $currency,
        public readonly string $accountNumber,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'WalletAccountCreated';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
