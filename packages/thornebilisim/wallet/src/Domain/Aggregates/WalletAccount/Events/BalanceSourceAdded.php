<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Source Added Event.
 *
 * Fired when a new balance source is added to track balances from different payment methods.
 */
class BalanceSourceAdded extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $method,
        public readonly Money $initialBalance,
        public readonly array $methodDetails,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceSourceAdded';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
