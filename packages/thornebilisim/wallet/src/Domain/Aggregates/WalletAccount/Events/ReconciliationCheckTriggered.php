<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\Enums\WalletAccount\ReconciliationType;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Reconciliation Check Triggered Event.
 *
 * Fired when a reconciliation check is triggered for balance verification.
 */
class ReconciliationCheckTriggered extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly ReconciliationType $reconciliationType,
        public readonly string $triggerReason,
        public readonly string $correlationId,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'ReconciliationCheckTriggered';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
