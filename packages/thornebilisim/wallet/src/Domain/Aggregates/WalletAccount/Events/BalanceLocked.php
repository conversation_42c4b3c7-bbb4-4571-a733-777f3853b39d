<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Locked Event.
 *
 * Fired when balance is locked for pending operations.
 */
class BalanceLocked extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly Money $amount,
        public readonly string $lockId,
        public readonly string $reason,
        public readonly Money $previousLockedBalance,
        public readonly Money $newLockedBalance,
        public readonly ?string $expiresAt,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceLocked';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
