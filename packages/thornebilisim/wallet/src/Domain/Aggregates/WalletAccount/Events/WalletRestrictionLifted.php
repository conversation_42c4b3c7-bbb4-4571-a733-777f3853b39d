<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Wallet Restriction Lifted Event.
 *
 * Fired when a restriction is manually lifted from a wallet account.
 */
class WalletRestrictionLifted extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $restrictionUuid,
        public readonly ?string $reason,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'WalletRestrictionLifted';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
