<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Wallet Restriction Failed Event.
 *
 * Fired when a restriction application or operation fails.
 */
class WalletRestrictionFailed extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $restrictionUuid,
        public readonly string $failureReason,
        public readonly string $attemptedOperation,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'WalletRestrictionFailed';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
