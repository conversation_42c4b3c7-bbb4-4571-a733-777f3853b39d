<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\Enums\WalletAccount\DiscrepancyDirection;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Reconciliation Check Completed Event.
 *
 * Fired when a reconciliation check is completed with comparison results.
 */
class ReconciliationCheckCompleted extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $reconciliationId,
        public readonly bool $isMatched,
        public readonly Money $web3Balance,
        public readonly Money $dbBalance,
        public readonly ?Money $discrepancyAmount,
        public readonly ?DiscrepancyDirection $discrepancyDirection,
        public readonly string $correlationId,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'ReconciliationCheckCompleted';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
