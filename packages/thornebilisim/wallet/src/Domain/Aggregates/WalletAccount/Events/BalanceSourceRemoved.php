<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Balance Source Removed Event.
 *
 * Fired when a balance source (payment method) is removed from the account.
 */
class BalanceSourceRemoved extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $method,
        public readonly string $removalReason,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceSourceRemoved';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
