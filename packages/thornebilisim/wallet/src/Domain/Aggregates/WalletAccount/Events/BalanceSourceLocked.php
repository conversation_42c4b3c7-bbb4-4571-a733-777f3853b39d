<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use DateTimeInterface;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Source Locked Event.
 *
 * Fired when balance from a specific source (method) is locked for pending operations.
 */
class BalanceSourceLocked extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $method,
        public readonly Money $lockedAmount,
        public readonly DateTimeInterface $unlockAt,
        public readonly bool $sync,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }

    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }

    public function getEventName(): string
    {
        return 'BalanceSourceLocked';
    }

    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
