<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Source Updated Event.
 *
 * Fired when a balance source is updated with new balance information.
 */
class BalanceSourceUpdated extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $method,
        public readonly Money $previousBalance,
        public readonly Money $newBalance,
        public readonly Money $changeAmount,
        public readonly string $transactionId,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceSourceUpdated';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
