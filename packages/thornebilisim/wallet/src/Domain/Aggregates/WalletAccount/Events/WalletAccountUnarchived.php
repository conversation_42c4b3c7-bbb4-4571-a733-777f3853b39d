<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Carbon\Carbon;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * WalletAccount Unarchived Event.
 *
 * Fired when a wallet account is unarchived and restored to active status.
 */
class WalletAccountUnarchived extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly Carbon $previousArchivedAt,
        public readonly ?string $reason,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'WalletAccountUnarchived';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
