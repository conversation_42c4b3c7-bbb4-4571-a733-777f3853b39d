<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Reconciliation Completed Event.
 *
 * Fired when a reconciliation process is completed with final resolution.
 */
class ReconciliationCompleted extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $reconciliationId,
        public readonly string $resolutionType,
        public readonly array $resolutionData,
        public readonly string $correlationId,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'ReconciliationCompleted';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
