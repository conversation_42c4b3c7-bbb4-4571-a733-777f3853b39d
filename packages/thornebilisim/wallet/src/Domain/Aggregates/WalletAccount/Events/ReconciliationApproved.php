<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Reconciliation Approved Event.
 *
 * Fired when a reconciliation discrepancy is approved for resolution.
 */
class ReconciliationApproved extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $reconciliationId,
        public readonly string $approvalReason,
        public readonly bool $autoApproved,
        public readonly string $correlationId,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'ReconciliationApproved';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
