<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Carbon\Carbon;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Balance Source Web3 Sync Updated Event.
 *
 * Fired when Web3 synchronization metadata is updated for a balance source.
 */
class BalanceSourceWeb3SyncUpdated extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $method,
        public readonly array $web3SyncMetadata,
        public readonly Carbon $syncedAt,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceSourceWeb3SyncUpdated';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
