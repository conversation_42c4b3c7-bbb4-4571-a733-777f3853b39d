<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Adjusted Manually Event.
 *
 * Fired when account balance is manually adjusted by administrators.
 */
class BalanceAdjustedManually extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly Money $newBalance,
        public readonly Money $previousBalance,
        public readonly string $reason,
        public readonly bool $isLowBalance,
        public readonly bool $sync,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceAdjustedManually';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
