<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\Enums\WalletAccount\AccountStatus;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * WalletAccount Status Changed Event.
 *
 * Fired when a wallet account status changes (ACTIVE, SUSPENDED, CLOSED).
 */
class WalletAccountStatusChanged extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly AccountStatus $previousStatus,
        public readonly AccountStatus $newStatus,
        public readonly ?string $reason,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'WalletAccountStatusChanged';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
