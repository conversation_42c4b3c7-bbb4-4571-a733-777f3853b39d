<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Unlocked Event.
 *
 * Fired when previously locked balance is released.
 */
class BalanceUnlocked extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly Money $amount,
        public readonly string $lockId,
        public readonly string $reason,
        public readonly Money $previousLockedBalance,
        public readonly Money $newLockedBalance,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceUnlocked';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
