<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Balance Source Unlocked Event.
 *
 * Fired when previously locked balance from a specific source is released.
 */
class BalanceSourceUnlocked extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $method,
        public readonly bool $sync,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }

    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }

    public function getEventName(): string
    {
        return 'BalanceSourceUnlocked';
    }

    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
