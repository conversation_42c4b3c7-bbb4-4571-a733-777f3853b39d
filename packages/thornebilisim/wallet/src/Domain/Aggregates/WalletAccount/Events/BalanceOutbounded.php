<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Outbounded Event.
 *
 * Fired when balance is removed from the wallet account from a specific method.
 */
class BalanceOutbounded extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly Money $amount,
        public readonly string $method,
        public readonly Money $newBalance,
        public readonly bool $isLowBalance,
        public readonly bool $sync,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }

    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }

    public function getEventName(): string
    {
        return 'BalanceOutbounded';
    }

    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
