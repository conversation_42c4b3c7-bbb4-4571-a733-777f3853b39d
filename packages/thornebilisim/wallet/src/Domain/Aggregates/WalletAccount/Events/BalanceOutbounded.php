<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\Enums\WalletTransaction\TransactionType;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Outbounded Event.
 *
 * Fired when balance is removed from the wallet account.
 */
class BalanceOutbounded extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly Money $amount,
        public readonly TransactionType $transactionType,
        public readonly string $transactionId,
        public readonly Money $previousBalance,
        public readonly Money $newBalance,
        public readonly ?string $description,
        public readonly bool $sync,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceOutbounded';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
