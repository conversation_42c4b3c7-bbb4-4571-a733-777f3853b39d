<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * Balance Updated From Reconciliation Event.
 *
 * Fired when account balance is updated as a result of reconciliation resolution.
 */
class BalanceUpdatedFromReconciliation extends BaseWalletEvent
{
    public function __construct(
        public readonly string $accountUuid,
        public readonly string $reconciliationId,
        public readonly Money $adjustmentAmount,
        public readonly Money $newBalance,
        public readonly string $reconciliationReason,
        public readonly string $correlationId,
        Metadata $metadata,
        int $eventVersion = 1
    ) {
        parent::__construct($metadata, $eventVersion);
    }
    
    public function getEventVersion(): int
    {
        return $this->eventVersion;
    }
    
    public function getEventName(): string
    {
        return 'BalanceUpdatedFromReconciliation';
    }
    
    public function getAggregateRootId(): string
    {
        return $this->accountUuid;
    }
}
