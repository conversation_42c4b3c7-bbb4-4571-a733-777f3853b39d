<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Events;

use Spatie\EventSourcing\StoredEvents\ShouldBeStored;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * Base Wallet Event.
 *
 * Provides common structure and versioning support for all wallet events.
 */
abstract class BaseWalletEvent implements ShouldBeStored
{
    public function __construct(
        public readonly Metadata $metadata,
        public readonly int $eventVersion = 1
    ) {}
    
    /**
     * Get the event version for backward compatibility.
     */
    abstract public function getEventVersion(): int;
    
    /**
     * Get the event name for identification.
     */
    abstract public function getEventName(): string;
    
    /**
     * Get the aggregate root ID this event belongs to.
     */
    abstract public function getAggregateRootId(): string;
}
