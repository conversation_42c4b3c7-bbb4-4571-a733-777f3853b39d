<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount;

use Carbon\Carbon;
use Ramsey\Uuid\Uuid;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceInbounded;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceOutbounded;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceLocked;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceUnlocked;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceSourceAdded;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceSourceUpdated;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletAccountCreated;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletAccountStatusChanged;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletAccountArchived;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletRestrictionApplied;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletRestrictionLifted;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletRestrictionExpired;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletAccountUnarchived;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationCheckTriggered;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationCheckCompleted;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationApproved;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationRejected;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationCompleted;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceUpdatedFromReconciliation;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials\WalletAccountLifecyclePartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials\WalletBalancePartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials\WalletBalanceSourcePartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials\WalletReconciliationPartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials\WalletRestrictionPartial;
use Thorne\Wallet\Domain\Enums\Shared\ArchiveStatus;
use Thorne\Wallet\Domain\Enums\WalletAccount\AccountStatus;
use Thorne\Wallet\Domain\Enums\WalletAccount\ReconciliationStatus;
use Thorne\Wallet\Domain\Enums\WalletAccount\RestrictionStatus;
use Thorne\Wallet\Domain\Exceptions\WalletDomainException;
use Thorne\Wallet\Domain\ValueObjects\Shared\Currency;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;
use Thorne\Wallet\Domain\ValueObjects\WalletAccount\AccountNumber;

/**
 * WalletAccount Aggregate Root.
 *
 * Represents a customer's wallet for a specific currency. Central point for managing
 * account lifecycle, status, restrictions, and balances. Enforces all invariants
 * related to the account state using the AggregatePartial pattern.
 */
class WalletAccountAggregate extends BaseWalletAggregate
{
    // Core properties
    private string $customerId;
    private Currency $currency;
    private AccountNumber $accountNumber;
    private AccountStatus $status;
    private ArchiveStatus $archiveStatus;
    private ?Carbon $archivedAt = null;
    
    // Balance tracking
    private Money $totalBalance;
    private Money $lockedBalance;
    private array $balanceSources = [];
    
    // Restrictions
    private array $restrictions = [];

    // Reconciliations
    private array $reconciliations = [];

    // Aggregate partials
    private WalletAccountLifecyclePartial $lifecyclePartial;
    private WalletBalancePartial $balancePartial;
    private WalletBalanceSourcePartial $balanceSourcePartial;
    private WalletRestrictionPartial $restrictionPartial;
    private WalletReconciliationPartial $reconciliationPartial;
    
    /**
     * Create a new wallet account.
     */
    public static function createAccount(
        string $customerId,
        Currency $currency,
        AccountNumber $accountNumber,
        Metadata $metadata
    ): self {
        $uuid = Uuid::uuid4()->toString();
        $aggregate = new static();
        $aggregate->aggregateRootUuid = $uuid;
        
        $aggregate->recordThat(new WalletAccountCreated(
            accountUuid: $uuid,
            customerId: $customerId,
            currency: $currency->getCode(),
            accountNumber: $accountNumber->getNumber(),
            metadata: $metadata
        ));
        
        return $aggregate;
    }
    
    /**
     * Change account status.
     */
    public function changeStatus(AccountStatus $newStatus, ?string $reason, Metadata $metadata): void
    {
        $this->validateStatusChange($newStatus);
        
        $previousStatus = $this->status;
        
        $this->recordThat(new WalletAccountStatusChanged(
            accountUuid: $this->uuid(),
            previousStatus: $previousStatus,
            newStatus: $newStatus,
            reason: $reason,
            metadata: $metadata
        ));
    }
    
    /**
     * Archive the account.
     */
    public function archive(?string $reason, Metadata $metadata): void
    {
        $this->lifecycle()->archive($reason, $metadata);
    }

    /**
     * Unarchive the account.
     */
    public function unarchive(?string $reason, Metadata $metadata): void
    {
        $this->lifecycle()->unarchive($reason, $metadata);
    }
    
    /**
     * Get lifecycle partial for lifecycle operations.
     */
    public function lifecycle(): WalletAccountLifecyclePartial
    {
        return $this->lifecyclePartial ??= new WalletAccountLifecyclePartial($this);
    }

    /**
     * Get balance partial for balance operations.
     */
    public function balance(): WalletBalancePartial
    {
        return $this->balancePartial ??= new WalletBalancePartial($this);
    }

    /**
     * Get balance source partial for source operations.
     */
    public function balanceSource(): WalletBalanceSourcePartial
    {
        return $this->balanceSourcePartial ??= new WalletBalanceSourcePartial($this);
    }

    /**
     * Get restriction partial for restriction operations.
     */
    public function restriction(): WalletRestrictionPartial
    {
        return $this->restrictionPartial ??= new WalletRestrictionPartial($this);
    }

    /**
     * Get reconciliation partial for reconciliation operations.
     */
    public function reconciliation(): WalletReconciliationPartial
    {
        return $this->reconciliationPartial ??= new WalletReconciliationPartial($this);
    }
    
    // Event application methods
    
    public function applyWalletAccountCreated(WalletAccountCreated $event): void
    {
        $this->customerId = $event->customerId;
        $this->currency = Currency::from($event->currency);
        $this->accountNumber = AccountNumber::fromString($event->accountNumber);
        $this->status = AccountStatus::ACTIVE;
        $this->archiveStatus = ArchiveStatus::ACTIVE;
        $this->totalBalance = Money::zero($event->currency);
        $this->lockedBalance = Money::zero($event->currency);
    }
    
    public function applyWalletAccountStatusChanged(WalletAccountStatusChanged $event): void
    {
        $this->status = $event->newStatus;
    }
    
    public function applyWalletAccountArchived(WalletAccountArchived $event): void
    {
        $this->archiveStatus = ArchiveStatus::ARCHIVED;
        $this->archivedAt = $event->archivedAt;
    }

    public function applyWalletAccountUnarchived(WalletAccountUnarchived $event): void
    {
        $this->archiveStatus = ArchiveStatus::ACTIVE;
        $this->archivedAt = null;
    }

    // Balance event applications

    public function applyBalanceInbounded(BalanceInbounded $event): void
    {
        $this->totalBalance = $event->newBalance;
    }

    public function applyBalanceOutbounded(BalanceOutbounded $event): void
    {
        $this->totalBalance = $event->newBalance;
    }

    public function applyBalanceLocked(BalanceLocked $event): void
    {
        $this->lockedBalance = $event->newLockedBalance;
    }

    public function applyBalanceUnlocked(BalanceUnlocked $event): void
    {
        $this->lockedBalance = $event->newLockedBalance;
    }

    // Balance source event applications

    public function applyBalanceSourceAdded(BalanceSourceAdded $event): void
    {
        $this->balanceSources[$event->method] = [
            'balance' => $event->initialBalance,
            'details' => $event->methodDetails,
            'created_at' => $event->metadata->getOccurredAt(),
            'updated_at' => $event->metadata->getOccurredAt(),
        ];
    }

    public function applyBalanceSourceUpdated(BalanceSourceUpdated $event): void
    {
        if (isset($this->balanceSources[$event->method])) {
            $this->balanceSources[$event->method]['balance'] = $event->newBalance;
            $this->balanceSources[$event->method]['updated_at'] = $event->metadata->getOccurredAt();
            $this->balanceSources[$event->method]['last_transaction_id'] = $event->transactionId;
        }
    }

    // Restriction event applications

    public function applyWalletRestrictionApplied(WalletRestrictionApplied $event): void
    {
        $this->restrictions[$event->restrictionUuid] = [
            'uuid' => $event->restrictionUuid,
            'type' => $event->restrictionType,
            'reason' => $event->restrictionReason,
            'status' => RestrictionStatus::APPLIED,
            'expires_at' => $event->expiresAt,
            'description' => $event->description,
            'applied_at' => $event->metadata->getOccurredAt(),
            'lifted_at' => null,
            'expired_at' => null,
        ];
    }

    public function applyWalletRestrictionLifted(WalletRestrictionLifted $event): void
    {
        if (isset($this->restrictions[$event->restrictionUuid])) {
            $this->restrictions[$event->restrictionUuid]['status'] = RestrictionStatus::LIFTED;
            $this->restrictions[$event->restrictionUuid]['lifted_at'] = $event->metadata->getOccurredAt();
            $this->restrictions[$event->restrictionUuid]['lift_reason'] = $event->reason;
        }
    }

    public function applyWalletRestrictionExpired(WalletRestrictionExpired $event): void
    {
        if (isset($this->restrictions[$event->restrictionUuid])) {
            $this->restrictions[$event->restrictionUuid]['status'] = RestrictionStatus::EXPIRED;
            $this->restrictions[$event->restrictionUuid]['expired_at'] = $event->expiredAt;
        }
    }

    // Reconciliation event applications

    public function applyReconciliationCheckTriggered(ReconciliationCheckTriggered $event): void
    {
        $this->reconciliations[$event->correlationId] = [
            'correlation_id' => $event->correlationId,
            'type' => $event->reconciliationType,
            'trigger_reason' => $event->triggerReason,
            'status' => ReconciliationStatus::PENDING,
            'triggered_at' => $event->metadata->getOccurredAt(),
            'completed_at' => null,
        ];
    }

    public function applyReconciliationCheckCompleted(ReconciliationCheckCompleted $event): void
    {
        if (isset($this->reconciliations[$event->correlationId])) {
            $this->reconciliations[$event->correlationId]['reconciliation_id'] = $event->reconciliationId;
            $this->reconciliations[$event->correlationId]['is_matched'] = $event->isMatched;
            $this->reconciliations[$event->correlationId]['web3_balance'] = $event->web3Balance;
            $this->reconciliations[$event->correlationId]['db_balance'] = $event->dbBalance;
            $this->reconciliations[$event->correlationId]['discrepancy_amount'] = $event->discrepancyAmount;
            $this->reconciliations[$event->correlationId]['discrepancy_direction'] = $event->discrepancyDirection;
            $this->reconciliations[$event->correlationId]['status'] = ReconciliationStatus::CHECKING;
            $this->reconciliations[$event->correlationId]['completed_at'] = $event->metadata->getOccurredAt();
        }
    }

    public function applyReconciliationApproved(ReconciliationApproved $event): void
    {
        if (isset($this->reconciliations[$event->correlationId])) {
            $this->reconciliations[$event->correlationId]['status'] = ReconciliationStatus::APPROVED;
            $this->reconciliations[$event->correlationId]['approval_reason'] = $event->approvalReason;
            $this->reconciliations[$event->correlationId]['auto_approved'] = $event->autoApproved;
            $this->reconciliations[$event->correlationId]['approved_at'] = $event->metadata->getOccurredAt();
        }
    }

    public function applyReconciliationRejected(ReconciliationRejected $event): void
    {
        if (isset($this->reconciliations[$event->correlationId])) {
            $this->reconciliations[$event->correlationId]['status'] = ReconciliationStatus::REJECTED;
            $this->reconciliations[$event->correlationId]['rejection_reason'] = $event->rejectionReason;
            $this->reconciliations[$event->correlationId]['rejected_at'] = $event->metadata->getOccurredAt();
        }
    }

    public function applyReconciliationCompleted(ReconciliationCompleted $event): void
    {
        if (isset($this->reconciliations[$event->correlationId])) {
            $this->reconciliations[$event->correlationId]['resolution_type'] = $event->resolutionType;
            $this->reconciliations[$event->correlationId]['resolution_data'] = $event->resolutionData;
            $this->reconciliations[$event->correlationId]['completed_at'] = $event->metadata->getOccurredAt();
        }
    }

    public function applyBalanceUpdatedFromReconciliation(BalanceUpdatedFromReconciliation $event): void
    {
        $this->totalBalance = $event->newBalance;

        if (isset($this->reconciliations[$event->correlationId])) {
            $this->reconciliations[$event->correlationId]['adjustment_amount'] = $event->adjustmentAmount;
            $this->reconciliations[$event->correlationId]['reconciliation_reason'] = $event->reconciliationReason;
            $this->reconciliations[$event->correlationId]['balance_updated_at'] = $event->metadata->getOccurredAt();
        }
    }
    
    // Getters for testing and partials
    
    public function getCustomerId(): string
    {
        return $this->customerId;
    }
    
    public function getCurrency(): Currency
    {
        return $this->currency;
    }
    
    public function getAccountNumber(): AccountNumber
    {
        return $this->accountNumber;
    }
    
    public function getStatus(): AccountStatus
    {
        return $this->status;
    }
    
    public function getArchiveStatus(): ArchiveStatus
    {
        return $this->archiveStatus;
    }
    
    public function getArchivedAt(): ?Carbon
    {
        return $this->archivedAt;
    }
    
    public function getTotalBalance(): Money
    {
        return $this->totalBalance;
    }
    
    public function getLockedBalance(): Money
    {
        return $this->lockedBalance;
    }
    
    public function getAvailableBalance(): Money
    {
        return $this->totalBalance->minus($this->lockedBalance);
    }
    
    public function getBalanceSources(): array
    {
        return $this->balanceSources;
    }
    
    public function getRestrictions(): array
    {
        return $this->restrictions;
    }

    public function getReconciliations(): array
    {
        return $this->reconciliations;
    }
    
    // Business invariant validation
    
    protected function validateInvariants(): void
    {
        // Balance cannot be negative
        if ($this->totalBalance->isNegative()) {
            throw WalletDomainException::negativeBalance($this->uuid());
        }
        
        // Locked balance cannot exceed total balance
        if ($this->lockedBalance->isGreaterThan($this->totalBalance)) {
            throw WalletDomainException::lockedBalanceExceedsTotal($this->uuid());
        }
        
        // Currency consistency
        if (!$this->totalBalance->getCurrency()->equals($this->currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
    }
    
    private function validateStatusChange(AccountStatus $newStatus): void
    {
        if ($this->status === $newStatus) {
            throw WalletDomainException::statusAlreadySet($this->uuid(), $newStatus->value);
        }
        
        if ($this->archiveStatus === ArchiveStatus::ARCHIVED) {
            throw WalletDomainException::cannotChangeArchivedAccountStatus($this->uuid());
        }
    }
}
