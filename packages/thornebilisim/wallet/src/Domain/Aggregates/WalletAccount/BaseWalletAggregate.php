<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount;

use Spatie\EventSourcing\AggregateRoots\AggregateRoot;

/**
 * Base Wallet Aggregate.
 *
 * Provides shared functionality for all wallet aggregates including
 * event handling, validation, and helper methods.
 */
abstract class BaseWalletAggregate extends AggregateRoot
{
    /**
     * Helper method to get the last recorded event of specific type.
     */
    public function getLastRecordedEvent(string $eventClass): ?object
    {
        $events = array_reverse($this->recordedEvents);

        foreach ($events as $event) {
            if ($event instanceof $eventClass) {
                return $event;
            }
        }

        return null;
    }

    /**
     * Apply multiple events in sequence.
     */
    protected function recordMultipleEvents(array $events): void
    {
        foreach ($events as $event) {
            $this->recordThat($event);
        }
    }

    /**
     * Get aggregate state property for testing and partials.
     */
    public function getState(string $property): mixed
    {
        return $this->{$property} ?? null;
    }

    /**
     * Validate business rules before recording events.
     */
    abstract protected function validateInvariants(): void;

    /**
     * Get the aggregate UUID.
     */
    public function uuid(): string
    {
        return $this->aggregateRootUuid();
    }
}
