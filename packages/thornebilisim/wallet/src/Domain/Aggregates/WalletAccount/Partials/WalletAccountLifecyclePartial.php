<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials;

use Carbon\Carbon;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\BaseAggregatePartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletAccountCreated;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletAccountStatusChanged;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletAccountArchived;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletAccountUnarchived;
use Thorne\Wallet\Domain\Enums\Shared\ArchiveStatus;
use Thorne\Wallet\Domain\Enums\WalletAccount\AccountStatus;
use Thorne\Wallet\Domain\Exceptions\WalletDomainException;
use Thorne\Wallet\Domain\ValueObjects\Shared\Currency;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\WalletAccount\AccountNumber;

/**
 * WalletAccountLifecycle Partial.
 *
 * Manages account lifecycle operations including account creation, status changes
 * (ACTIVE, SUSPENDED, CLOSED), and archival processes.
 */
class WalletAccountLifecyclePartial extends BaseAggregatePartial
{
    /**
     * Create a new wallet account.
     */
    public function createAccount(
        string $customerId,
        Currency $currency,
        AccountNumber $accountNumber,
        Metadata $metadata
    ): void {
        $this->validateAccountCreation($customerId, $currency, $accountNumber);
        
        $this->recordThat(new WalletAccountCreated(
            accountUuid: $this->uuid(),
            customerId: $customerId,
            currency: $currency->getCode(),
            accountNumber: $accountNumber->getNumber(),
            metadata: $metadata
        ));
    }
    
    /**
     * Change account status.
     */
    public function changeStatus(
        AccountStatus $newStatus,
        ?string $reason,
        Metadata $metadata
    ): void {
        $this->validateStatusChange($newStatus);
        
        $previousStatus = $this->getState('status');
        
        $this->recordThat(new WalletAccountStatusChanged(
            accountUuid: $this->uuid(),
            previousStatus: $previousStatus,
            newStatus: $newStatus,
            reason: $reason,
            metadata: $metadata
        ));
    }
    
    /**
     * Archive the account.
     */
    public function archive(?string $reason, Metadata $metadata): void
    {
        $this->validateArchive();
        
        $archivedAt = Carbon::now();
        
        $this->recordThat(new WalletAccountArchived(
            accountUuid: $this->uuid(),
            archivedAt: $archivedAt,
            reason: $reason,
            metadata: $metadata
        ));
    }
    
    /**
     * Unarchive the account.
     */
    public function unarchive(?string $reason, Metadata $metadata): void
    {
        $this->validateUnarchive();
        
        $previousArchivedAt = $this->getState('archivedAt');
        
        $this->recordThat(new WalletAccountUnarchived(
            accountUuid: $this->uuid(),
            previousArchivedAt: $previousArchivedAt,
            reason: $reason,
            metadata: $metadata
        ));
    }
    
    /**
     * Check if account is active and operational.
     */
    public function isOperational(): bool
    {
        $status = $this->getState('status');
        $archiveStatus = $this->getState('archiveStatus');
        
        return $status === AccountStatus::ACTIVE && $archiveStatus === ArchiveStatus::ACTIVE;
    }
    
    /**
     * Check if account can perform transactions.
     */
    public function canPerformTransactions(): bool
    {
        return $this->isOperational();
    }
    
    /**
     * Check if account is archived.
     */
    public function isArchived(): bool
    {
        $archiveStatus = $this->getState('archiveStatus');
        return $archiveStatus === ArchiveStatus::ARCHIVED;
    }
    
    /**
     * Check if account is suspended.
     */
    public function isSuspended(): bool
    {
        $status = $this->getState('status');
        return $status === AccountStatus::SUSPENDED;
    }
    
    /**
     * Check if account is closed.
     */
    public function isClosed(): bool
    {
        $status = $this->getState('status');
        return $status === AccountStatus::CLOSED;
    }
    
    /**
     * Get account creation date.
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->getState('createdAt');
    }
    
    /**
     * Get account archived date.
     */
    public function getArchivedAt(): ?Carbon
    {
        return $this->getState('archivedAt');
    }
    
    // Note: Event application methods are handled by the main aggregate
    
    // Validation methods
    
    protected function validateDomain(): void
    {
        $status = $this->getState('status');
        $archiveStatus = $this->getState('archiveStatus');
        
        // Archived accounts should not be active
        if ($archiveStatus === ArchiveStatus::ARCHIVED && $status === AccountStatus::ACTIVE) {
            throw WalletDomainException::archivedAccountCannotBeActive($this->uuid());
        }
    }
    
    private function validateAccountCreation(string $customerId, Currency $currency, AccountNumber $accountNumber): void
    {
        if (empty($customerId)) {
            throw WalletDomainException::invalidCustomerId($this->uuid());
        }
        
        if (!$accountNumber->isValid()) {
            throw WalletDomainException::invalidAccountNumber($this->uuid());
        }
        
        if (!$accountNumber->belongsToCurrency($currency->getCode())) {
            throw WalletDomainException::accountNumberCurrencyMismatch($this->uuid());
        }
    }
    
    private function validateStatusChange(AccountStatus $newStatus): void
    {
        $currentStatus = $this->getState('status');
        
        if ($currentStatus === $newStatus) {
            throw WalletDomainException::statusAlreadySet($this->uuid(), $newStatus->value);
        }
        
        if ($this->isArchived()) {
            throw WalletDomainException::cannotChangeArchivedAccountStatus($this->uuid());
        }
        
        // Business rule: Cannot reactivate a closed account
        if ($currentStatus === AccountStatus::CLOSED && $newStatus === AccountStatus::ACTIVE) {
            throw WalletDomainException::cannotReactivateClosedAccount($this->uuid());
        }
    }
    
    private function validateArchive(): void
    {
        if ($this->isArchived()) {
            throw WalletDomainException::accountAlreadyArchived($this->uuid());
        }
    }
    
    private function validateUnarchive(): void
    {
        if (!$this->isArchived()) {
            throw WalletDomainException::accountNotArchived($this->uuid());
        }
    }
}
