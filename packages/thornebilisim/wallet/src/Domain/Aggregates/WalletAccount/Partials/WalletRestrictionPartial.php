<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials;

use Carbon\Carbon;
use Ramsey\Uuid\Uuid;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\BaseAggregatePartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletRestrictionApplied;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletRestrictionLifted;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\WalletRestrictionExpired;
use Thorne\Wallet\Domain\Enums\WalletAccount\RestrictionReason;
use Thorne\Wallet\Domain\Enums\WalletAccount\RestrictionStatus;
use Thorne\Wallet\Domain\Enums\WalletAccount\RestrictionType;
use Thorne\Wallet\Domain\Exceptions\WalletDomainException;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;

/**
 * WalletRestriction Partial.
 *
 * Enforces operation restrictions with proper lifecycle management.
 * Handles applying, lifting, and expiring restrictions on wallet operations.
 */
class WalletRestrictionPartial extends BaseAggregatePartial
{
    /**
     * Apply a restriction to the wallet account.
     */
    public function applyRestriction(
        RestrictionType $restrictionType,
        RestrictionReason $restrictionReason,
        Metadata $metadata,
        ?Carbon $expiresAt = null,
        ?string $description = null
    ): void {
        $this->validateApplyRestriction($restrictionType, $restrictionReason);
        
        $restrictionUuid = Uuid::uuid4()->toString();
        
        $this->recordThat(new WalletRestrictionApplied(
            accountUuid: $this->uuid(),
            restrictionUuid: $restrictionUuid,
            restrictionType: $restrictionType,
            restrictionReason: $restrictionReason,
            expiresAt: $expiresAt,
            description: $description,
            metadata: $metadata
        ));
    }
    
    /**
     * Manually lift a restriction.
     */
    public function liftRestriction(
        string $restrictionUuid,
        Metadata $metadata,
        ?string $reason = null
    ): void {
        $this->validateLiftRestriction($restrictionUuid);
        
        $this->recordThat(new WalletRestrictionLifted(
            accountUuid: $this->uuid(),
            restrictionUuid: $restrictionUuid,
            reason: $reason,
            metadata: $metadata
        ));
    }
    
    /**
     * Mark a restriction as expired (typically called by saga).
     */
    public function expireRestriction(
        string $restrictionUuid,
        Carbon $expiredAt,
        Metadata $metadata
    ): void {
        $this->validateExpireRestriction($restrictionUuid);
        
        $this->recordThat(new WalletRestrictionExpired(
            accountUuid: $this->uuid(),
            restrictionUuid: $restrictionUuid,
            expiredAt: $expiredAt,
            metadata: $metadata
        ));
    }
    
    /**
     * Check if a specific operation type is restricted.
     */
    public function isOperationRestricted(RestrictionType $operationType): bool
    {
        $restrictions = $this->getActiveRestrictions();
        
        foreach ($restrictions as $restriction) {
            if ($restriction['type'] === RestrictionType::ALL || $restriction['type'] === $operationType) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get all active restrictions.
     */
    public function getActiveRestrictions(): array
    {
        $restrictions = $this->getState('restrictions') ?? [];
        
        return array_filter($restrictions, function ($restriction) {
            return $restriction['status'] === RestrictionStatus::APPLIED;
        });
    }
    
    /**
     * Get all restrictions (active and inactive).
     */
    public function getAllRestrictions(): array
    {
        return $this->getState('restrictions') ?? [];
    }
    
    /**
     * Check if account has any active restrictions.
     */
    public function hasActiveRestrictions(): bool
    {
        return !empty($this->getActiveRestrictions());
    }
    
    /**
     * Get restriction by UUID.
     */
    public function getRestriction(string $restrictionUuid): ?array
    {
        $restrictions = $this->getState('restrictions') ?? [];
        return $restrictions[$restrictionUuid] ?? null;
    }
    
    /**
     * Check if account has specific restriction type active.
     */
    public function hasRestrictionType(RestrictionType $restrictionType): bool
    {
        $activeRestrictions = $this->getActiveRestrictions();
        
        foreach ($activeRestrictions as $restriction) {
            if ($restriction['type'] === $restrictionType || $restriction['type'] === RestrictionType::ALL) {
                return true;
            }
        }
        
        return false;
    }
    
    // Note: Event application methods are handled by the main aggregate
    
    // Validation methods
    
    protected function validateDomain(): void
    {
        $restrictions = $this->getState('restrictions') ?? [];
        
        // Validate that expired restrictions are properly marked
        foreach ($restrictions as $restriction) {
            if ($restriction['status'] === RestrictionStatus::APPLIED && 
                $restriction['expires_at'] && 
                $restriction['expires_at']->isPast()) {
                // This should be handled by saga, but we can log a warning
                \Log::warning('Restriction should be expired but still active', [
                    'account_uuid' => $this->uuid(),
                    'restriction_uuid' => $restriction['uuid'],
                    'expires_at' => $restriction['expires_at']->toISOString(),
                ]);
            }
        }
    }
    
    private function validateApplyRestriction(RestrictionType $restrictionType, RestrictionReason $restrictionReason): void
    {
        // Check if similar restriction already exists
        $activeRestrictions = $this->getActiveRestrictions();
        
        foreach ($activeRestrictions as $restriction) {
            if ($restriction['type'] === $restrictionType && $restriction['reason'] === $restrictionReason) {
                throw WalletDomainException::restrictionAlreadyExists($this->uuid(), $restrictionType->value);
            }
        }
        
        // Check if ALL restriction exists (no point in adding specific restrictions)
        if ($restrictionType !== RestrictionType::ALL && $this->hasRestrictionType(RestrictionType::ALL)) {
            throw WalletDomainException::allRestrictionsAlreadyApplied($this->uuid());
        }
    }
    
    private function validateLiftRestriction(string $restrictionUuid): void
    {
        $restriction = $this->getRestriction($restrictionUuid);
        
        if (!$restriction) {
            throw WalletDomainException::restrictionNotFound($this->uuid(), $restrictionUuid);
        }
        
        if ($restriction['status'] !== RestrictionStatus::APPLIED) {
            throw WalletDomainException::restrictionNotActive($this->uuid(), $restrictionUuid);
        }
    }
    
    private function validateExpireRestriction(string $restrictionUuid): void
    {
        $restriction = $this->getRestriction($restrictionUuid);
        
        if (!$restriction) {
            throw WalletDomainException::restrictionNotFound($this->uuid(), $restrictionUuid);
        }
        
        if ($restriction['status'] !== RestrictionStatus::APPLIED) {
            throw WalletDomainException::restrictionNotActive($this->uuid(), $restrictionUuid);
        }
        
        if (!$restriction['expires_at']) {
            throw WalletDomainException::restrictionHasNoExpiration($this->uuid(), $restrictionUuid);
        }
    }
}
