<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials;

use Thorne\Wallet\Domain\Aggregates\WalletAccount\BaseAggregatePartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceInbounded;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceOutbounded;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceLocked;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceUnlocked;
use Thorne\Wallet\Domain\Enums\WalletTransaction\TransactionType;
use Thorne\Wallet\Domain\Exceptions\WalletDomainException;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * WalletBalance Partial.
 *
 * Manages available and locked balance tracking with DECIMAL(20,8) precision.
 * Handles balance inbound/outbound operations and balance locking mechanisms.
 */
class WalletBalancePartial extends BaseAggregatePartial
{
    /**
     * Add balance to the account (inbound operation).
     */
    public function addBalance(
        Money $amount,
        TransactionType $transactionType,
        string $transactionId,
        ?string $description = null,
        bool $sync = false,
        Metadata $metadata
    ): void {
        $this->validateInboundOperation($amount);
        
        $previousBalance = $this->getState('totalBalance');
        $newBalance = $previousBalance->plus($amount);
        
        $this->recordThat(new BalanceInbounded(
            accountUuid: $this->uuid(),
            amount: $amount,
            transactionType: $transactionType,
            transactionId: $transactionId,
            previousBalance: $previousBalance,
            newBalance: $newBalance,
            description: $description,
            sync: $sync,
            metadata: $metadata
        ));
    }
    
    /**
     * Remove balance from the account (outbound operation).
     */
    public function removeBalance(
        Money $amount,
        TransactionType $transactionType,
        string $transactionId,
        ?string $description = null,
        bool $sync = false,
        Metadata $metadata
    ): void {
        $this->validateOutboundOperation($amount);
        
        $previousBalance = $this->getState('totalBalance');
        $newBalance = $previousBalance->minus($amount);
        
        $this->recordThat(new BalanceOutbounded(
            accountUuid: $this->uuid(),
            amount: $amount,
            transactionType: $transactionType,
            transactionId: $transactionId,
            previousBalance: $previousBalance,
            newBalance: $newBalance,
            description: $description,
            sync: $sync,
            metadata: $metadata
        ));
    }
    
    /**
     * Lock balance for pending operations.
     */
    public function lockBalance(
        Money $amount,
        string $lockId,
        string $reason,
        ?string $expiresAt = null,
        Metadata $metadata
    ): void {
        $this->validateLockOperation($amount);
        
        $previousLockedBalance = $this->getState('lockedBalance');
        $newLockedBalance = $previousLockedBalance->plus($amount);
        
        $this->recordThat(new BalanceLocked(
            accountUuid: $this->uuid(),
            amount: $amount,
            lockId: $lockId,
            reason: $reason,
            previousLockedBalance: $previousLockedBalance,
            newLockedBalance: $newLockedBalance,
            expiresAt: $expiresAt,
            metadata: $metadata
        ));
    }
    
    /**
     * Unlock previously locked balance.
     */
    public function unlockBalance(
        Money $amount,
        string $lockId,
        string $reason,
        Metadata $metadata
    ): void {
        $this->validateUnlockOperation($amount);
        
        $previousLockedBalance = $this->getState('lockedBalance');
        $newLockedBalance = $previousLockedBalance->minus($amount);
        
        $this->recordThat(new BalanceUnlocked(
            accountUuid: $this->uuid(),
            amount: $amount,
            lockId: $lockId,
            reason: $reason,
            previousLockedBalance: $previousLockedBalance,
            newLockedBalance: $newLockedBalance,
            metadata: $metadata
        ));
    }
    
    /**
     * Get available balance (total - locked).
     */
    public function getAvailableBalance(): Money
    {
        $totalBalance = $this->getState('totalBalance');
        $lockedBalance = $this->getState('lockedBalance');
        
        return $totalBalance->minus($lockedBalance);
    }
    
    /**
     * Check if sufficient balance is available for operation.
     */
    public function hasSufficientBalance(Money $amount): bool
    {
        return $this->getAvailableBalance()->isGreaterThanOrEqual($amount);
    }
    
    /**
     * Check if sufficient locked balance exists for unlock operation.
     */
    public function hasSufficientLockedBalance(Money $amount): bool
    {
        $lockedBalance = $this->getState('lockedBalance');
        return $lockedBalance->isGreaterThanOrEqual($amount);
    }
    
    // Note: Event application methods are handled by the main aggregate
    
    // Validation methods
    
    protected function validateDomain(): void
    {
        $totalBalance = $this->getState('totalBalance');
        $lockedBalance = $this->getState('lockedBalance');
        
        if ($totalBalance && $totalBalance->isNegative()) {
            throw WalletDomainException::negativeBalance($this->uuid());
        }
        
        if ($lockedBalance && $totalBalance && $lockedBalance->isGreaterThan($totalBalance)) {
            throw WalletDomainException::lockedBalanceExceedsTotal($this->uuid());
        }
    }
    
    private function validateInboundOperation(Money $amount): void
    {
        if ($amount->isNegativeOrZero()) {
            throw WalletDomainException::invalidInboundAmount($this->uuid());
        }
        
        $currency = $this->getState('currency');
        if ($currency && !$amount->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
    }
    
    private function validateOutboundOperation(Money $amount): void
    {
        if ($amount->isNegativeOrZero()) {
            throw WalletDomainException::invalidOutboundAmount($this->uuid());
        }
        
        if (!$this->hasSufficientBalance($amount)) {
            throw WalletDomainException::insufficientBalance($this->uuid());
        }
        
        $currency = $this->getState('currency');
        if ($currency && !$amount->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
    }
    
    private function validateLockOperation(Money $amount): void
    {
        if ($amount->isNegativeOrZero()) {
            throw WalletDomainException::invalidLockAmount($this->uuid());
        }
        
        if (!$this->hasSufficientBalance($amount)) {
            throw WalletDomainException::insufficientBalanceForLock($this->uuid());
        }
        
        $currency = $this->getState('currency');
        if ($currency && !$amount->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
    }
    
    private function validateUnlockOperation(Money $amount): void
    {
        if ($amount->isNegativeOrZero()) {
            throw WalletDomainException::invalidUnlockAmount($this->uuid());
        }
        
        if (!$this->hasSufficientLockedBalance($amount)) {
            throw WalletDomainException::insufficientLockedBalance($this->uuid());
        }
        
        $currency = $this->getState('currency');
        if ($currency && !$amount->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
    }
}
