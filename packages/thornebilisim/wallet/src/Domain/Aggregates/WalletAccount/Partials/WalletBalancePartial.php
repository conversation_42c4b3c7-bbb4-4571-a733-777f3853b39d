<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials;

use Thorne\Wallet\Domain\Aggregates\WalletAccount\BaseAggregatePartial;
use DateTimeInterface;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceInbounded;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceOutbounded;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceSourceLocked;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceSourceUnlocked;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceAdjustedManually;

use Thorne\Wallet\Domain\Exceptions\WalletDomainException;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * WalletBalance Partial.
 *
 * Manages available and locked balance tracking with DECIMAL(20,8) precision.
 * Handles balance inbound/outbound operations and balance locking mechanisms.
 */
class WalletBalancePartial extends BaseAggregatePartial
{
    /**
     * Process inbound balance from a specific method.
     */
    public function processInboundBalance(
        Money $amount,
        string $method,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->validateInboundOperation($amount, $method);

        $previousBalance = $this->getState('totalBalance');
        $newBalance = $previousBalance->plus($amount);
        $isLowBalance = $this->isLowBalance($newBalance);

        $this->recordThat(new BalanceInbounded(
            accountUuid: $this->uuid(),
            amount: $amount,
            method: $method,
            newBalance: $newBalance,
            isLowBalance: $isLowBalance,
            sync: $sync,
            metadata: $metadata
        ));
    }

    /**
     * Process outbound balance from a specific method.
     */
    public function processOutboundBalance(
        Money $amount,
        string $method,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->validateOutboundOperation($amount, $method);

        $previousBalance = $this->getState('totalBalance');
        $newBalance = $previousBalance->minus($amount);
        $isLowBalance = $this->isLowBalance($newBalance);

        $this->recordThat(new BalanceOutbounded(
            accountUuid: $this->uuid(),
            amount: $amount,
            method: $method,
            newBalance: $newBalance,
            isLowBalance: $isLowBalance,
            sync: $sync,
            metadata: $metadata
        ));
    }
    
    /**
     * Lock balance from a specific source for pending operations.
     */
    public function lockBalanceSource(
        string $method,
        Money $amount,
        DateTimeInterface $unlockAt,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->validateLockSourceOperation($method, $amount);

        $this->recordThat(new BalanceSourceLocked(
            accountUuid: $this->uuid(),
            method: $method,
            lockedAmount: $amount,
            unlockAt: $unlockAt,
            sync: $sync,
            metadata: $metadata
        ));
    }

    /**
     * Unlock previously locked balance from a specific source.
     */
    public function unlockBalanceSource(
        string $method,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->validateUnlockSourceOperation($method);

        $this->recordThat(new BalanceSourceUnlocked(
            accountUuid: $this->uuid(),
            method: $method,
            sync: $sync,
            metadata: $metadata
        ));
    }

    /**
     * Manually adjust balance (admin operation).
     */
    public function adjustBalanceManually(
        Money $newBalance,
        string $reason,
        bool $isLowBalance,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->validateManualAdjustment($newBalance, $reason);

        $previousBalance = $this->getState('totalBalance');

        $this->recordThat(new BalanceAdjustedManually(
            accountUuid: $this->uuid(),
            newBalance: $newBalance,
            previousBalance: $previousBalance,
            reason: $reason,
            isLowBalance: $isLowBalance,
            sync: $sync,
            metadata: $metadata
        ));
    }

    // Backward compatibility methods (deprecated - use processInboundBalance/processOutboundBalance)

    /**
     * @deprecated Use processInboundBalance() instead
     */
    public function addBalance(
        Money $amount,
        string $method,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->processInboundBalance($amount, $method, $sync, $metadata);
    }

    /**
     * @deprecated Use processOutboundBalance() instead
     */
    public function removeBalance(
        Money $amount,
        string $method,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->processOutboundBalance($amount, $method, $sync, $metadata);
    }

    /**
     * @deprecated Use lockBalanceSource() instead
     */
    public function lockBalance(
        string $method,
        Money $amount,
        DateTimeInterface $unlockAt,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->lockBalanceSource($method, $amount, $unlockAt, $sync, $metadata);
    }

    /**
     * @deprecated Use unlockBalanceSource() instead
     */
    public function unlockBalance(
        string $method,
        bool $sync,
        Metadata $metadata
    ): void {
        $this->unlockBalanceSource($method, $sync, $metadata);
    }
    
    /**
     * Get available balance (total - locked).
     */
    public function getAvailableBalance(): Money
    {
        $totalBalance = $this->getState('totalBalance');
        $lockedBalance = $this->getState('lockedBalance');
        
        return $totalBalance->minus($lockedBalance);
    }
    
    /**
     * Check if sufficient balance is available for operation.
     */
    public function hasSufficientBalance(Money $amount): bool
    {
        return $this->getAvailableBalance()->isGreaterThanOrEqual($amount);
    }
    
    /**
     * Check if sufficient locked balance exists for unlock operation.
     */
    public function hasSufficientLockedBalance(Money $amount): bool
    {
        $lockedBalance = $this->getState('lockedBalance');
        return $lockedBalance->isGreaterThanOrEqual($amount);
    }

    /**
     * Check if the balance is considered low.
     */
    private function isLowBalance(Money $balance): bool
    {
        // This could be configurable via WalletConfig
        // For now, consider balance low if it's less than 10 units
        $lowBalanceThreshold = Money::of(10, $balance->getCurrency()->getCode());
        return $balance->isLessThan($lowBalanceThreshold);
    }
    
    // Note: Event application methods are handled by the main aggregate
    
    // Validation methods
    
    protected function validateDomain(): void
    {
        $totalBalance = $this->getState('totalBalance');
        $lockedBalance = $this->getState('lockedBalance');
        
        if ($totalBalance && $totalBalance->isNegative()) {
            throw WalletDomainException::negativeBalance($this->uuid());
        }
        
        if ($lockedBalance && $totalBalance && $lockedBalance->isGreaterThan($totalBalance)) {
            throw WalletDomainException::lockedBalanceExceedsTotal($this->uuid());
        }
    }
    
    private function validateInboundOperation(Money $amount, string $method): void
    {
        if ($amount->isNegativeOrZero()) {
            throw WalletDomainException::invalidInboundAmount($this->uuid());
        }

        if (empty($method)) {
            throw WalletDomainException::invalidBalanceSourceMethod($this->uuid());
        }

        $currency = $this->getState('currency');
        if ($currency && !$amount->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }

        // Check if the source exists
        $balanceSources = $this->getState('balanceSources') ?? [];
        if (!isset($balanceSources[$method])) {
            throw WalletDomainException::balanceSourceNotFound($this->uuid(), $method);
        }
    }

    private function validateOutboundOperation(Money $amount, string $method): void
    {
        if ($amount->isNegativeOrZero()) {
            throw WalletDomainException::invalidOutboundAmount($this->uuid());
        }

        if (empty($method)) {
            throw WalletDomainException::invalidBalanceSourceMethod($this->uuid());
        }

        $currency = $this->getState('currency');
        if ($currency && !$amount->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }

        // Check if the source exists and has sufficient balance
        $balanceSources = $this->getState('balanceSources') ?? [];
        if (!isset($balanceSources[$method])) {
            throw WalletDomainException::balanceSourceNotFound($this->uuid(), $method);
        }

        $sourceBalance = $balanceSources[$method]['balance'] ?? Money::zero($amount->getCurrency()->getCode());
        $sourceLockedBalance = $balanceSources[$method]['locked_balance'] ?? Money::zero($amount->getCurrency()->getCode());
        $sourceAvailableBalance = $sourceBalance->minus($sourceLockedBalance);

        if ($sourceAvailableBalance->isLessThan($amount)) {
            throw WalletDomainException::insufficientSourceBalance($this->uuid(), $method);
        }
    }
    
    private function validateLockSourceOperation(string $method, Money $amount): void
    {
        if (empty($method)) {
            throw WalletDomainException::invalidBalanceSourceMethod($this->uuid());
        }

        if ($amount->isNegativeOrZero()) {
            throw WalletDomainException::invalidLockAmount($this->uuid());
        }

        // Check if the source exists and has sufficient balance
        $balanceSources = $this->getState('balanceSources') ?? [];
        if (!isset($balanceSources[$method])) {
            throw WalletDomainException::balanceSourceNotFound($this->uuid(), $method);
        }

        $sourceBalance = $balanceSources[$method]['balance'] ?? Money::zero($amount->getCurrency()->getCode());
        $sourceLockedBalance = $balanceSources[$method]['locked_balance'] ?? Money::zero($amount->getCurrency()->getCode());
        $sourceAvailableBalance = $sourceBalance->minus($sourceLockedBalance);

        if ($sourceAvailableBalance->isLessThan($amount)) {
            throw WalletDomainException::insufficientSourceBalanceForLock($this->uuid(), $method);
        }

        $currency = $this->getState('currency');
        if ($currency && !$amount->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
    }

    private function validateUnlockSourceOperation(string $method): void
    {
        if (empty($method)) {
            throw WalletDomainException::invalidBalanceSourceMethod($this->uuid());
        }

        // Check if the source exists and has locked balance
        $balanceSources = $this->getState('balanceSources') ?? [];
        if (!isset($balanceSources[$method])) {
            throw WalletDomainException::balanceSourceNotFound($this->uuid(), $method);
        }

        $sourceLockedBalance = $balanceSources[$method]['locked_balance'] ?? Money::zero($this->getState('currency')->getCode());
        if ($sourceLockedBalance->isZero()) {
            throw WalletDomainException::noLockedBalanceToUnlock($this->uuid(), $method);
        }
    }

    private function validateManualAdjustment(Money $newBalance, string $reason): void
    {
        if (empty($reason)) {
            throw WalletDomainException::invalidManualAdjustmentReason($this->uuid());
        }

        if ($newBalance->isNegative()) {
            throw WalletDomainException::manualAdjustmentResultsInNegativeBalance($this->uuid());
        }

        $currency = $this->getState('currency');
        if ($currency && !$newBalance->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
    }
}
