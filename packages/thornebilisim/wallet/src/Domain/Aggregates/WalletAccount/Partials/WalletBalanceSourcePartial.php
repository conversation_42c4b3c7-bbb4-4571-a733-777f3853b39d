<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials;

use Thorne\Wallet\Domain\Aggregates\WalletAccount\BaseAggregatePartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceSourceAdded;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceSourceUpdated;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceSourceRemoved;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceSourceWeb3SyncUpdated;
use Carbon\Carbon;
use Thorne\Wallet\Domain\Exceptions\WalletDomainException;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * WalletBalanceSource Partial.
 *
 * Handles balance source management from different payment methods.
 * Tracks balances from various sources like bank transfers, card payments, etc.
 */
class WalletBalanceSourcePartial extends BaseAggregatePartial
{
    /**
     * Add a new balance source.
     */
    public function addSource(
        string $method,
        Money $initialBalance,
        array $methodDetails,
        Metadata $metadata
    ): void {
        $this->validateAddSource($method, $initialBalance);
        
        $this->recordThat(new BalanceSourceAdded(
            accountUuid: $this->uuid(),
            method: $method,
            initialBalance: $initialBalance,
            methodDetails: $methodDetails,
            metadata: $metadata
        ));
    }
    
    /**
     * Update balance for an existing source.
     */
    public function updateSource(
        string $method,
        Money $changeAmount,
        string $transactionId,
        Metadata $metadata
    ): void {
        $this->validateUpdateSource($method, $changeAmount);

        $balanceSources = $this->getState('balanceSources');
        $previousBalance = $balanceSources[$method]['balance'] ?? Money::zero($changeAmount->getCurrency()->getCode());
        $newBalance = $previousBalance->plus($changeAmount);

        $this->recordThat(new BalanceSourceUpdated(
            accountUuid: $this->uuid(),
            method: $method,
            previousBalance: $previousBalance,
            newBalance: $newBalance,
            changeAmount: $changeAmount,
            transactionId: $transactionId,
            metadata: $metadata
        ));
    }

    /**
     * Remove a balance source.
     */
    public function removeSource(
        string $method,
        string $removalReason,
        Metadata $metadata
    ): void {
        $this->validateRemoveSource($method, $removalReason);

        $this->recordThat(new BalanceSourceRemoved(
            accountUuid: $this->uuid(),
            method: $method,
            removalReason: $removalReason,
            metadata: $metadata
        ));
    }

    /**
     * Update Web3 sync metadata for a source.
     */
    public function updateWeb3SyncMetadata(
        string $method,
        array $web3SyncMetadata,
        Carbon $syncedAt,
        Metadata $metadata
    ): void {
        $this->validateWeb3SyncUpdate($method, $web3SyncMetadata);

        $this->recordThat(new BalanceSourceWeb3SyncUpdated(
            accountUuid: $this->uuid(),
            method: $method,
            web3SyncMetadata: $web3SyncMetadata,
            syncedAt: $syncedAt,
            metadata: $metadata
        ));
    }
    
    /**
     * Get balance for a specific source.
     */
    public function getSourceBalance(string $method): ?Money
    {
        $balanceSources = $this->getState('balanceSources');
        return $balanceSources[$method]['balance'] ?? null;
    }
    
    /**
     * Get all balance sources.
     */
    public function getAllSources(): array
    {
        return $this->getState('balanceSources') ?? [];
    }
    
    /**
     * Check if a source exists.
     */
    public function hasSource(string $method): bool
    {
        $balanceSources = $this->getState('balanceSources');
        return isset($balanceSources[$method]);
    }
    
    /**
     * Get total balance across all sources.
     */
    public function getTotalSourceBalance(): Money
    {
        $balanceSources = $this->getState('balanceSources');
        $currency = $this->getState('currency');
        $total = Money::zero($currency->getCode());
        
        foreach ($balanceSources as $source) {
            $total = $total->plus($source['balance']);
        }
        
        return $total;
    }
    
    /**
     * Get source details.
     */
    public function getSourceDetails(string $method): ?array
    {
        $balanceSources = $this->getState('balanceSources');
        return $balanceSources[$method]['details'] ?? null;
    }
    
    // Note: Event application methods are handled by the main aggregate
    
    // Validation methods
    
    protected function validateDomain(): void
    {
        $balanceSources = $this->getState('balanceSources');
        $currency = $this->getState('currency');
        
        if ($balanceSources && $currency) {
            foreach ($balanceSources as $method => $source) {
                if (!$source['balance']->getCurrency()->equals($currency)) {
                    throw WalletDomainException::balanceSourceCurrencyMismatch($this->uuid(), $method);
                }
            }
        }
    }
    
    private function validateAddSource(string $method, Money $initialBalance): void
    {
        if (empty($method)) {
            throw WalletDomainException::invalidBalanceSourceMethod($this->uuid());
        }
        
        if ($this->hasSource($method)) {
            throw WalletDomainException::balanceSourceAlreadyExists($this->uuid(), $method);
        }
        
        if ($initialBalance->isNegative()) {
            throw WalletDomainException::negativeInitialBalance($this->uuid(), $method);
        }
        
        $currency = $this->getState('currency');
        if ($currency && !$initialBalance->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
    }
    
    private function validateUpdateSource(string $method, Money $changeAmount): void
    {
        if (empty($method)) {
            throw WalletDomainException::invalidBalanceSourceMethod($this->uuid());
        }
        
        if (!$this->hasSource($method)) {
            throw WalletDomainException::balanceSourceNotFound($this->uuid(), $method);
        }
        
        if ($changeAmount->isZero()) {
            throw WalletDomainException::zeroChangeAmount($this->uuid(), $method);
        }
        
        $currency = $this->getState('currency');
        if ($currency && !$changeAmount->getCurrency()->equals($currency)) {
            throw WalletDomainException::currencyMismatch($this->uuid());
        }
        
        // Check if the change would result in negative balance
        $currentBalance = $this->getSourceBalance($method);
        if ($currentBalance && $changeAmount->isNegative() && $currentBalance->plus($changeAmount)->isNegative()) {
            throw WalletDomainException::balanceSourceWouldBeNegative($this->uuid(), $method);
        }
    }

    private function validateRemoveSource(string $method, string $removalReason): void
    {
        if (empty($method)) {
            throw WalletDomainException::invalidBalanceSourceMethod($this->uuid());
        }

        if (!$this->hasSource($method)) {
            throw WalletDomainException::balanceSourceNotFound($this->uuid(), $method);
        }

        if (empty($removalReason)) {
            throw WalletDomainException::invalidBalanceSourceRemovalReason($this->uuid(), $method);
        }
    }

    private function validateWeb3SyncUpdate(string $method, array $web3SyncMetadata): void
    {
        if (empty($method)) {
            throw WalletDomainException::invalidBalanceSourceMethod($this->uuid());
        }

        if (!$this->hasSource($method)) {
            throw WalletDomainException::balanceSourceNotFound($this->uuid(), $method);
        }

        if (empty($web3SyncMetadata)) {
            throw WalletDomainException::invalidWeb3SyncMetadata($this->uuid(), $method);
        }
    }
}
