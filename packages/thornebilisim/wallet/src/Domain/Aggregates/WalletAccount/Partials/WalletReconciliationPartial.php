<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Aggregates\WalletAccount\Partials;

use <PERSON>\Uuid\Uuid;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\BaseAggregatePartial;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationCheckTriggered;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationCheckCompleted;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationApproved;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationRejected;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\ReconciliationCompleted;
use Thorne\Wallet\Domain\Aggregates\WalletAccount\Events\BalanceUpdatedFromReconciliation;
use Thorne\Wallet\Domain\Enums\WalletAccount\DiscrepancyDirection;
use Thorne\Wallet\Domain\Enums\WalletAccount\ReconciliationStatus;
use Thorne\Wallet\Domain\Enums\WalletAccount\ReconciliationType;
use Thorne\Wallet\Domain\Exceptions\WalletDomainException;
use Thorne\Wallet\Domain\ValueObjects\Shared\Metadata;
use Thorne\Wallet\Domain\ValueObjects\Shared\Money;

/**
 * WalletReconciliation Partial.
 *
 * Manages balance reconciliation processes with Web3 services and external systems.
 * Handles reconciliation triggers, completion, approval/rejection, and balance updates.
 */
class WalletReconciliationPartial extends BaseAggregatePartial
{
    /**
     * Trigger a reconciliation check.
     */
    public function triggerReconciliationCheck(
        ReconciliationType $reconciliationType,
        string $triggerReason,
        string $correlationId,
        Metadata $metadata
    ): void {
        $this->validateTriggerReconciliation($reconciliationType, $triggerReason, $correlationId);
        
        $this->recordThat(new ReconciliationCheckTriggered(
            accountUuid: $this->uuid(),
            reconciliationType: $reconciliationType,
            triggerReason: $triggerReason,
            correlationId: $correlationId,
            metadata: $metadata
        ));
    }
    
    /**
     * Complete a reconciliation check with comparison results.
     */
    public function completeReconciliationCheck(
        string $reconciliationId,
        bool $isMatched,
        Money $web3Balance,
        Money $dbBalance,
        string $correlationId,
        Metadata $metadata,
        ?Money $discrepancyAmount = null,
        ?DiscrepancyDirection $discrepancyDirection = null
    ): void {
        $this->validateCompleteReconciliation($reconciliationId, $web3Balance, $dbBalance, $correlationId);
        
        $this->recordThat(new ReconciliationCheckCompleted(
            accountUuid: $this->uuid(),
            reconciliationId: $reconciliationId,
            isMatched: $isMatched,
            web3Balance: $web3Balance,
            dbBalance: $dbBalance,
            discrepancyAmount: $discrepancyAmount,
            discrepancyDirection: $discrepancyDirection,
            correlationId: $correlationId,
            metadata: $metadata
        ));
    }
    
    /**
     * Approve a reconciliation discrepancy for resolution.
     */
    public function approveReconciliation(
        string $reconciliationId,
        string $approvalReason,
        bool $autoApproved,
        string $correlationId,
        Metadata $metadata
    ): void {
        $this->validateApproveReconciliation($reconciliationId, $approvalReason, $correlationId);
        
        $this->recordThat(new ReconciliationApproved(
            accountUuid: $this->uuid(),
            reconciliationId: $reconciliationId,
            approvalReason: $approvalReason,
            autoApproved: $autoApproved,
            correlationId: $correlationId,
            metadata: $metadata
        ));
    }
    
    /**
     * Reject a reconciliation discrepancy.
     */
    public function rejectReconciliation(
        string $reconciliationId,
        string $rejectionReason,
        string $correlationId,
        Metadata $metadata
    ): void {
        $this->validateRejectReconciliation($reconciliationId, $rejectionReason, $correlationId);
        
        $this->recordThat(new ReconciliationRejected(
            accountUuid: $this->uuid(),
            reconciliationId: $reconciliationId,
            rejectionReason: $rejectionReason,
            correlationId: $correlationId,
            metadata: $metadata
        ));
    }
    
    /**
     * Complete a reconciliation process with final resolution.
     */
    public function completeReconciliation(
        string $reconciliationId,
        string $resolutionType,
        array $resolutionData,
        string $correlationId,
        Metadata $metadata
    ): void {
        $this->validateCompleteReconciliationProcess($reconciliationId, $resolutionType, $correlationId);
        
        $this->recordThat(new ReconciliationCompleted(
            accountUuid: $this->uuid(),
            reconciliationId: $reconciliationId,
            resolutionType: $resolutionType,
            resolutionData: $resolutionData,
            correlationId: $correlationId,
            metadata: $metadata
        ));
    }
    
    /**
     * Update balance from reconciliation resolution.
     */
    public function updateBalanceFromReconciliation(
        string $reconciliationId,
        Money $adjustmentAmount,
        Money $newBalance,
        string $reconciliationReason,
        string $correlationId,
        Metadata $metadata
    ): void {
        $this->validateBalanceUpdateFromReconciliation($reconciliationId, $adjustmentAmount, $newBalance, $correlationId);
        
        $this->recordThat(new BalanceUpdatedFromReconciliation(
            accountUuid: $this->uuid(),
            reconciliationId: $reconciliationId,
            adjustmentAmount: $adjustmentAmount,
            newBalance: $newBalance,
            reconciliationReason: $reconciliationReason,
            correlationId: $correlationId,
            metadata: $metadata
        ));
    }
    
    /**
     * Get active reconciliation processes.
     */
    public function getActiveReconciliations(): array
    {
        $reconciliations = $this->getState('reconciliations') ?? [];
        
        return array_filter($reconciliations, function ($reconciliation) {
            return in_array($reconciliation['status'], [
                ReconciliationStatus::PENDING,
                ReconciliationStatus::CHECKING
            ]);
        });
    }
    
    /**
     * Check if account has pending reconciliations.
     */
    public function hasPendingReconciliations(): bool
    {
        return !empty($this->getActiveReconciliations());
    }
    
    /**
     * Get reconciliation by ID.
     */
    public function getReconciliation(string $reconciliationId): ?array
    {
        $reconciliations = $this->getState('reconciliations') ?? [];
        return $reconciliations[$reconciliationId] ?? null;
    }
    
    /**
     * Check if reconciliation exists.
     */
    public function hasReconciliation(string $reconciliationId): bool
    {
        return $this->getReconciliation($reconciliationId) !== null;
    }
    
    // Note: Event application methods are handled by the main aggregate
    
    // Validation methods
    
    protected function validateDomain(): void
    {
        // Validate reconciliation state consistency
        $reconciliations = $this->getState('reconciliations') ?? [];
        
        foreach ($reconciliations as $reconciliation) {
            if ($reconciliation['status'] === ReconciliationStatus::CHECKING && 
                !isset($reconciliation['started_at'])) {
                throw WalletDomainException::invalidReconciliationState($this->uuid(), $reconciliation['id']);
            }
        }
    }
    
    private function validateTriggerReconciliation(ReconciliationType $reconciliationType, string $triggerReason, string $correlationId): void
    {
        if (empty($triggerReason)) {
            throw WalletDomainException::invalidReconciliationTriggerReason($this->uuid());
        }
        
        if (empty($correlationId)) {
            throw WalletDomainException::invalidReconciliationCorrelationId($this->uuid());
        }
        
        // Check if there's already an active reconciliation
        if ($this->hasPendingReconciliations()) {
            throw WalletDomainException::reconciliationAlreadyInProgress($this->uuid());
        }
    }
    
    private function validateCompleteReconciliation(string $reconciliationId, Money $web3Balance, Money $dbBalance, string $correlationId): void
    {
        if (empty($reconciliationId)) {
            throw WalletDomainException::invalidReconciliationId($this->uuid());
        }
        
        if (empty($correlationId)) {
            throw WalletDomainException::invalidReconciliationCorrelationId($this->uuid());
        }
        
        $currency = $this->getState('currency');
        if ($currency && (!$web3Balance->getCurrency()->equals($currency) || !$dbBalance->getCurrency()->equals($currency))) {
            throw WalletDomainException::reconciliationCurrencyMismatch($this->uuid());
        }
    }
    
    private function validateApproveReconciliation(string $reconciliationId, string $approvalReason, string $correlationId): void
    {
        if (empty($reconciliationId)) {
            throw WalletDomainException::invalidReconciliationId($this->uuid());
        }
        
        if (empty($approvalReason)) {
            throw WalletDomainException::invalidReconciliationApprovalReason($this->uuid());
        }
        
        if (empty($correlationId)) {
            throw WalletDomainException::invalidReconciliationCorrelationId($this->uuid());
        }
    }
    
    private function validateRejectReconciliation(string $reconciliationId, string $rejectionReason, string $correlationId): void
    {
        if (empty($reconciliationId)) {
            throw WalletDomainException::invalidReconciliationId($this->uuid());
        }
        
        if (empty($rejectionReason)) {
            throw WalletDomainException::invalidReconciliationRejectionReason($this->uuid());
        }
        
        if (empty($correlationId)) {
            throw WalletDomainException::invalidReconciliationCorrelationId($this->uuid());
        }
    }
    
    private function validateCompleteReconciliationProcess(string $reconciliationId, string $resolutionType, string $correlationId): void
    {
        if (empty($reconciliationId)) {
            throw WalletDomainException::invalidReconciliationId($this->uuid());
        }
        
        if (empty($resolutionType)) {
            throw WalletDomainException::invalidReconciliationResolutionType($this->uuid());
        }
        
        if (empty($correlationId)) {
            throw WalletDomainException::invalidReconciliationCorrelationId($this->uuid());
        }
    }
    
    private function validateBalanceUpdateFromReconciliation(string $reconciliationId, Money $adjustmentAmount, Money $newBalance, string $correlationId): void
    {
        if (empty($reconciliationId)) {
            throw WalletDomainException::invalidReconciliationId($this->uuid());
        }
        
        if (empty($correlationId)) {
            throw WalletDomainException::invalidReconciliationCorrelationId($this->uuid());
        }
        
        $currency = $this->getState('currency');
        if ($currency && (!$adjustmentAmount->getCurrency()->equals($currency) || !$newBalance->getCurrency()->equals($currency))) {
            throw WalletDomainException::reconciliationCurrencyMismatch($this->uuid());
        }
        
        if ($newBalance->isNegative()) {
            throw WalletDomainException::reconciliationResultsInNegativeBalance($this->uuid());
        }
    }
}
