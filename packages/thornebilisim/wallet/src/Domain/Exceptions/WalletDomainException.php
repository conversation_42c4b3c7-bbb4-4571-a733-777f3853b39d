<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Exceptions;

use Exception;
use Throwable;

/**
 * WalletDomainException.
 *
 * Base exception class for all wallet domain-related exceptions.
 * Provides consistent error handling and context management.
 */
class WalletDomainException extends Exception
{
    /**
     * Additional context data for the exception.
     */
    protected array $context;

    public function __construct(
        string $message = '',
        int $code = 0,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * Get the exception context.
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Set additional context for the exception.
     */
    public function setContext(array $context): self
    {
        $this->context = array_merge($this->context, $context);

        return $this;
    }

    /**
     * Convert the exception to an array for logging.
     */
    public function toArray(): array
    {
        return [
            'exception' => static::class,
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->context,
        ];
    }

    /**
     * Get a JSON representation of the exception.
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT);
    }

    // WalletAccount specific exceptions

    public static function negativeBalance(string $accountUuid): self
    {
        return new self(
            "Account balance cannot be negative",
            1001,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function lockedBalanceExceedsTotal(string $accountUuid): self
    {
        return new self(
            "Locked balance cannot exceed total balance",
            1002,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function currencyMismatch(string $accountUuid): self
    {
        return new self(
            "Currency mismatch in operation",
            1003,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function accountAlreadyArchived(string $accountUuid): self
    {
        return new self(
            "Account is already archived",
            1004,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function statusAlreadySet(string $accountUuid, string $status): self
    {
        return new self(
            "Account status is already set to {$status}",
            1005,
            null,
            ['account_uuid' => $accountUuid, 'status' => $status]
        );
    }

    public static function cannotChangeArchivedAccountStatus(string $accountUuid): self
    {
        return new self(
            "Cannot change status of archived account",
            1006,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidInboundAmount(string $accountUuid): self
    {
        return new self(
            "Inbound amount must be positive",
            1007,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidOutboundAmount(string $accountUuid): self
    {
        return new self(
            "Outbound amount must be positive",
            1008,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function insufficientBalance(string $accountUuid): self
    {
        return new self(
            "Insufficient balance for operation",
            1009,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidLockAmount(string $accountUuid): self
    {
        return new self(
            "Lock amount must be positive",
            1010,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function insufficientBalanceForLock(string $accountUuid): self
    {
        return new self(
            "Insufficient balance for lock operation",
            1011,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidUnlockAmount(string $accountUuid): self
    {
        return new self(
            "Unlock amount must be positive",
            1012,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function insufficientLockedBalance(string $accountUuid): self
    {
        return new self(
            "Insufficient locked balance for unlock operation",
            1013,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    // Balance Source specific exceptions

    public static function invalidBalanceSourceMethod(string $accountUuid): self
    {
        return new self(
            "Balance source method cannot be empty",
            1020,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function balanceSourceAlreadyExists(string $accountUuid, string $method): self
    {
        return new self(
            "Balance source method already exists",
            1021,
            null,
            ['account_uuid' => $accountUuid, 'method' => $method]
        );
    }

    public static function negativeInitialBalance(string $accountUuid, string $method): self
    {
        return new self(
            "Initial balance cannot be negative",
            1022,
            null,
            ['account_uuid' => $accountUuid, 'method' => $method]
        );
    }

    public static function balanceSourceNotFound(string $accountUuid, string $method): self
    {
        return new self(
            "Balance source not found",
            1023,
            null,
            ['account_uuid' => $accountUuid, 'method' => $method]
        );
    }

    public static function zeroChangeAmount(string $accountUuid, string $method): self
    {
        return new self(
            "Change amount cannot be zero",
            1024,
            null,
            ['account_uuid' => $accountUuid, 'method' => $method]
        );
    }

    public static function balanceSourceWouldBeNegative(string $accountUuid, string $method): self
    {
        return new self(
            "Operation would result in negative balance source",
            1025,
            null,
            ['account_uuid' => $accountUuid, 'method' => $method]
        );
    }

    public static function balanceSourceCurrencyMismatch(string $accountUuid, string $method): self
    {
        return new self(
            "Balance source currency does not match account currency",
            1026,
            null,
            ['account_uuid' => $accountUuid, 'method' => $method]
        );
    }

    // Restriction specific exceptions

    public static function restrictionAlreadyExists(string $accountUuid, string $restrictionType): self
    {
        return new self(
            "Restriction of this type already exists",
            1030,
            null,
            ['account_uuid' => $accountUuid, 'restriction_type' => $restrictionType]
        );
    }

    public static function allRestrictionsAlreadyApplied(string $accountUuid): self
    {
        return new self(
            "All operations are already restricted",
            1031,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function restrictionNotFound(string $accountUuid, string $restrictionUuid): self
    {
        return new self(
            "Restriction not found",
            1032,
            null,
            ['account_uuid' => $accountUuid, 'restriction_uuid' => $restrictionUuid]
        );
    }

    public static function restrictionNotActive(string $accountUuid, string $restrictionUuid): self
    {
        return new self(
            "Restriction is not active",
            1033,
            null,
            ['account_uuid' => $accountUuid, 'restriction_uuid' => $restrictionUuid]
        );
    }

    public static function restrictionHasNoExpiration(string $accountUuid, string $restrictionUuid): self
    {
        return new self(
            "Restriction has no expiration date",
            1034,
            null,
            ['account_uuid' => $accountUuid, 'restriction_uuid' => $restrictionUuid]
        );
    }

    // Lifecycle specific exceptions

    public static function archivedAccountCannotBeActive(string $accountUuid): self
    {
        return new self(
            "Archived account cannot be active",
            1040,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidCustomerId(string $accountUuid): self
    {
        return new self(
            "Customer ID cannot be empty",
            1041,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidAccountNumber(string $accountUuid): self
    {
        return new self(
            "Account number is invalid",
            1042,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function accountNumberCurrencyMismatch(string $accountUuid): self
    {
        return new self(
            "Account number does not match currency",
            1043,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function cannotReactivateClosedAccount(string $accountUuid): self
    {
        return new self(
            "Cannot reactivate a closed account",
            1044,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function accountNotArchived(string $accountUuid): self
    {
        return new self(
            "Account is not archived",
            1045,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    // Reconciliation specific exceptions

    public static function invalidReconciliationTriggerReason(string $accountUuid): self
    {
        return new self(
            "Reconciliation trigger reason cannot be empty",
            1050,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidReconciliationCorrelationId(string $accountUuid): self
    {
        return new self(
            "Reconciliation correlation ID cannot be empty",
            1051,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function reconciliationAlreadyInProgress(string $accountUuid): self
    {
        return new self(
            "Reconciliation is already in progress",
            1052,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidReconciliationId(string $accountUuid): self
    {
        return new self(
            "Reconciliation ID cannot be empty",
            1053,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function reconciliationCurrencyMismatch(string $accountUuid): self
    {
        return new self(
            "Reconciliation currency does not match account currency",
            1054,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidReconciliationApprovalReason(string $accountUuid): self
    {
        return new self(
            "Reconciliation approval reason cannot be empty",
            1055,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidReconciliationRejectionReason(string $accountUuid): self
    {
        return new self(
            "Reconciliation rejection reason cannot be empty",
            1056,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidReconciliationResolutionType(string $accountUuid): self
    {
        return new self(
            "Reconciliation resolution type cannot be empty",
            1057,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function reconciliationResultsInNegativeBalance(string $accountUuid): self
    {
        return new self(
            "Reconciliation would result in negative balance",
            1058,
            null,
            ['account_uuid' => $accountUuid]
        );
    }

    public static function invalidReconciliationState(string $accountUuid, string $reconciliationId): self
    {
        return new self(
            "Invalid reconciliation state",
            1059,
            null,
            ['account_uuid' => $accountUuid, 'reconciliation_id' => $reconciliationId]
        );
    }
}
